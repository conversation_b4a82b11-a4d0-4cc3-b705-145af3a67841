import { useState, useEffect } from 'react';

interface SidebarState {
  isLeftOpen: boolean;
  isRightOpen: boolean;
}

export const useSidebarState = (courseId: string) => {
  const [sidebarState, setSidebarState] = useState<SidebarState>({
    isLeftOpen: true,
    isRightOpen: true,
  });

  // Load state from localStorage on mount
  useEffect(() => {
    const savedState = localStorage.getItem(`sidebar-state-${courseId}`);
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        setSidebarState(parsed);
      } catch (error) {
        console.warn('Failed to parse saved sidebar state:', error);
      }
    }
  }, [courseId]);

  // Save state to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem(`sidebar-state-${courseId}`, JSON.stringify(sidebarState));
  }, [courseId, sidebarState]);

  const toggleLeft = () => {
    setSidebarState(prev => ({ ...prev, isLeftOpen: !prev.isLeftOpen }));
  };

  const toggleRight = () => {
    setSidebarState(prev => ({ ...prev, isRightOpen: !prev.isRightOpen }));
  };

  const setLeftOpen = (isOpen: boolean) => {
    setSidebarState(prev => ({ ...prev, isLeftOpen: isOpen }));
  };

  const setRightOpen = (isOpen: boolean) => {
    setSidebarState(prev => ({ ...prev, isRightOpen: isOpen }));
  };

  return {
    isLeftOpen: sidebarState.isLeftOpen,
    isRightOpen: sidebarState.isRightOpen,
    toggleLeft,
    toggleRight,
    setLeftOpen,
    setRightOpen,
  };
};