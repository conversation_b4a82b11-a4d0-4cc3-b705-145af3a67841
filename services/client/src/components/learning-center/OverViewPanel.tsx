import React from 'react';
import VerticalProgress from '../vertical-progress.component';
import { Course, Lesson } from '../../types/Course';
import { PanelWithHeader } from '../shared/PanelWithHeader';
import { ChevronLeft } from 'lucide-react';
interface OverViewPanelProps {
  course: Course;
  setSelectedLesson: (lesson: Lesson) => void;
  collapsible?: boolean;
  onToggle?: () => void;
}

const OverViewPanel: React.FC<OverViewPanelProps> = ({
  course,
  setSelectedLesson,
  collapsible = false,
  onToggle,
}) => {
  const headerButtons = collapsible && onToggle ? [
    {
      icon: <ChevronLeft className="h-4 w-4" />,
      onClick: onToggle,
      ariaLabel: 'Collapse course overview',
      variant: 'ghost' as const,
    },
  ] : undefined;

  return (
    <PanelWithHeader
      tabs={[
        { id: 'curriculum', label: 'Course Overview' },
        { id: 'progress', label: 'Progress' },
      ]}
      activeTab="curriculum"
      headerButtons={headerButtons}
    >
      <VerticalProgress
        modules={course.modules}
        currentModuleId={course.modules[0]._id} // TODO: Adjust as needed
        currentLessonId={course.modules[0].lessons[0]._id}
        onLessonSelect={(lesson) => setSelectedLesson(lesson)}
        completedLessons={course.userProgress?.completedLessons}
      />
    </PanelWithHeader>
  );
};

export default OverViewPanel;
