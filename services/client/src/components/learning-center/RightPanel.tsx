import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ChevronRight, MessageSquare, FileText, BookOpen } from 'lucide-react';
import { cn } from '@/lib/utils';

interface RightPanelProps {
  collapsible?: boolean;
  onToggle?: () => void;
}

const RightPanel: React.FC<RightPanelProps> = ({
  collapsible = false,
  onToggle,
}) => {
  const [activeTab, setActiveTab] = useState('ask');

  return (
    <div className="overflow-hidden h-full flex flex-col">
      {/* Header with Tabs and Toggle */}
      <div className="border-b border-gray-200">
        <div className="flex justify-between items-center px-4 py-3">
          {/* Tabs */}
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="ask" className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                Ask
              </TabsTrigger>
              <TabsTrigger value="notes" className="flex items-center gap-1">
                <FileText className="h-3 w-3" />
                Notes
              </TabsTrigger>
              <TabsTrigger value="resources" className="flex items-center gap-1">
                <BookOpen className="h-3 w-3" />
                Resources
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Collapse Toggle */}
          {collapsible && onToggle && (
            <Button
              variant="ghost"
              size="icon"
              onClick={onToggle}
              className="ml-2 h-8 w-8"
              aria-label="Collapse notes and resources"
              aria-expanded={true}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>

      {/* Tab Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsContent value="ask" className="flex-1 flex flex-col m-0">
          {/* Chat Area */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            <div className="text-sm text-muted-foreground text-center">
              Ask questions about this lesson...
            </div>
          </div>

          {/* Input Area */}
          <div className="border-t border-gray-200 p-4">
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder="Type your question..."
                className="flex-1 rounded-lg border border-input px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-ring"
              />
              <Button size="sm">Send</Button>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="notes" className="flex-1 overflow-y-auto p-4 m-0">
          <div className="text-sm text-muted-foreground text-center">
            Your notes for this lesson will appear here...
          </div>
        </TabsContent>

        <TabsContent value="resources" className="flex-1 overflow-y-auto p-4 m-0">
          <div className="text-sm text-muted-foreground text-center">
            Additional resources and materials...
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RightPanel;