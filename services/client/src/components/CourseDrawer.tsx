'use client';

import { Sheet, Sheet<PERSON>ontent, SheetHeader, SheetTitle } from '@/components/ui/sheet';
import { Course } from '../types/Course';
import { cn, devshackTheme, headerVariant, surfaceVariant, textVariant, withAnimatedEffects } from '@/lib/utils';


interface CourseDrawerProps {
  course: Course | null;
  onClose: () => void;
}

const CourseDrawer: React.FC<CourseDrawerProps> = ({ course, onClose }) => {
  return (
    <Sheet open={!!course} onOpenChange={onClose}>
      <SheetContent 
        side="right" 
        className={cn(
          "w-[400px] sm:w-[540px] overflow-y-auto",
          surfaceVariant('medium')
        )}
      >
        <SheetHeader className="space-y-4">
          <div className="flex items-center justify-between">
            <SheetTitle className={headerVariant('muted')}>
              {course?.title}
            </SheetTitle>
            <button
              onClick={onClose}
              className={cn(
                "opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-neonGreen-500 focus:ring-offset-2",
                devshackTheme.radius.button
              )}
            >
            
              <span className="sr-only">Close</span>
            </button>
          </div>
        </SheetHeader>

        {course && (
          <div className="mt-6 space-y-6">
            {/* Cover Image */}
            <div className={cn(
              "relative h-48 w-full overflow-hidden",
              devshackTheme.radius.card,
              withAnimatedEffects("", true)
            )}>
              <img
                alt={course.title}
                src={course.coverImage}
                className="h-full w-full object-cover"
              />
            </div>

            {/* Course Description */}
            <div className={cn(
              "p-4",
              surfaceVariant('light'),
              devshackTheme.radius.input
            )}>
              <p className={cn(textVariant('muted'), "leading-relaxed")}>
                {course.description}
              </p>
              <p className={cn(textVariant("accent"),"mt-2",withAnimatedEffects("", true))}>
                {course.creator}
              </p>
            </div>

            {/* Course Stats */}
            <div className="grid grid-cols-3 gap-4">
              <div className={cn(
                "text-center p-3",
                surfaceVariant('accent'),
                devshackTheme.radius.input,
                withAnimatedEffects("", true)
              )}>
                <div className={cn(textVariant('accent'), "text-lg")}>
                  {course.enrolments}
                </div>
                <div className={textVariant('muted')}>
                  Enrollments
                </div>
              </div>
              <div className={cn(
                "text-center p-3",
                surfaceVariant('accent'),
                devshackTheme.radius.input,
                withAnimatedEffects("", true)
              )}>
                <div className={cn(textVariant('accent'), "text-lg")}>
                  {course.rating} 
                </div>
                <div className={textVariant('muted')}>
                  Rating
                </div>
              </div>
              <div className={cn(
                "text-center p-3",
                surfaceVariant('accent'),
                devshackTheme.radius.input,
                withAnimatedEffects("", true)
              )}>
                <div className={cn(textVariant('accent'), "text-lg")}>
                  {course.lessonsCount}
                </div>
                <div className={textVariant('muted')}>
                  Lessons
                </div>
              </div>
            </div>

            {/* Action Button */}
            <div>
              <a
                href={`/learning/${course._id}`}
                className={cn(
                  "inline-flex items-center justify-center whitespace-nowrap text-sm font-medium ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-neonGreen-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 h-10 px-4 py-2 w-full",
                  "bg-neonGreen-500 text-black hover:bg-neonGreen-600 transition-colors",
                  devshackTheme.radius.button,
                  devshackTheme.shadows.neonGlow,
                  withAnimatedEffects("", true)
                )}
              >
                Continue Learning
              </a>
            </div>

            {/* Modules Section */}
            <div className="space-y-4">
              <h3 className={headerVariant('secondary')}>
                Modules
              </h3>
              <div className="space-y-2">
                {course.modules.map((module) => (
                  <div
                    key={module.module}
                    className={cn(
                      "flex items-start space-x-3 p-3",
                      surfaceVariant('light'),
                      "hover:bg-neonGreen-50 hover:border-neonGreen-200 transition-all duration-300",
                      devshackTheme.radius.input,
                      withAnimatedEffects("", true)
                    )}
                  >
                    <div className={cn(
                      "flex-shrink-0 w-8 h-8 flex items-center justify-center",
                      "bg-neonGreen-100 border border-neonGreen-300",
                      devshackTheme.radius.pill
                    )}>
                      <span className={cn(textVariant('accent'), "text-xs")}>
                        {module.module}
                      </span>
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={cn(textVariant('primary'), "text-sm")}>
                        Module {module.module}
                      </p>
                      <p className={cn(textVariant('muted'), "mt-1")}>
                        {module.title}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};

export default CourseDrawer;