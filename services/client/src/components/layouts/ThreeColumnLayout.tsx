import { ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '@/lib/utils';


interface ThreeColumnLayoutProps {
  left: ReactNode;
  center: ReactNode;
  right: ReactNode;
  gridGap?: number;
  leftColSpan?: number;
  centerColSpan?: number;
  rightColSpan?: number;
  panelPadding?: number;
  // Collapsible props
  isLeftOpen?: boolean;
  isRightOpen?: boolean;
  onLeftToggle?: () => void;
  onRightToggle?: () => void;
  leftCollapsible?: boolean;
  rightCollapsible?: boolean;
}

export default function ThreeColumnLayout({
  left,
  center,
  right,
  gridGap = 4,
  leftColSpan = 3,
  centerColSpan = 6,
  rightColSpan = 3,
  panelPadding = 4,
  isLeftOpen = true,
  isRightOpen = true,
  onLeftToggle,
  onRightToggle,
  leftCollapsible = false,
  rightCollapsible = false,
}: ThreeColumnLayoutProps) {
  // Use CSS variable `--nav-height` for the top nav's height.
  // Falls back to 60px if the variable isn't defined.\
  // Calculate dynamic column spans based on panel states
  const getColumnSpans = () => {
    const totalCols = 12;
    let leftSpan = isLeftOpen ? leftColSpan : 0;
    let rightSpan = isRightOpen ? rightColSpan : 0;
    let centerSpan = totalCols - leftSpan - rightSpan;
    
    return { leftSpan, centerSpan, rightSpan };
  };

  const { leftSpan, centerSpan, rightSpan } = getColumnSpans();

  return (
     <div
      className="bg-gray-200 mx-auto relative"
      style={{ height: 'calc(100vh - var(--nav-height, 60px))' }}
    >
      <div className={cn(
        "grid h-full p-4 transition-all duration-200 ease-in-out",
        `gap-${gridGap}`,
        "grid-cols-12"
      )}>
        
        {/* Left Panel */}
        {leftSpan > 0 && (
          <div className={cn(
            "bg-white rounded-md h-full overflow-auto transition-all duration-200 ease-in-out",
            `col-span-${leftSpan}`,
            `p-${panelPadding}`,
            "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
          )}>
            {left}
          </div>
        )}

        {/* Collapsed Left Panel Toggle */}
        {leftCollapsible && !isLeftOpen && (
          <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-20">
            <Button
              variant="outline"
              size="icon"
              onClick={onLeftToggle}
              className="h-12 w-12 bg-white shadow-lg hover:shadow-xl transition-shadow duration-200"
              aria-label="Expand course overview"
              aria-expanded={false}
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        )}
        
        {/* Center Panel */}
        <div className={cn(
          "overflow-auto h-full transition-all duration-200 ease-in-out",
          `col-span-${centerSpan}`,
          "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
        )}>
          {center}
        </div>
        
        {/* Right Panel */}
        {rightSpan > 0 && (
          <div className={cn(
            "bg-white rounded-md h-full overflow-auto transition-all duration-200 ease-in-out",
            `col-span-${rightSpan}`,
            `p-${panelPadding}`,
            "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
          )}>
            {right}
          </div>
        )}

        {/* Collapsed Right Panel Toggle */}
        {rightCollapsible && !isRightOpen && (
          <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-20">
            <Button
              variant="outline"
              size="icon"
              onClick={onRightToggle}
              className="h-12 w-12 bg-white shadow-lg hover:shadow-xl transition-shadow duration-200"
              aria-label="Expand notes and resources"
              aria-expanded={false}
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
