# Learning Center Enhancement Implementation Guide

## Overview
This guide provides detailed file-by-file implementation instructions for the learning center enhancements, including collapsible panels and integrated note-taking functionality using ShadCN UI components.

## Phase 1: ShadCN Component Setup

### Step 1: Add Missing ShadCN Components

```bash
# Navigate to client directory
cd services/client

# Add required ShadCN components
npx shadcn@latest add textarea
npx shadcn@latest add accordion  
npx shadcn@latest add resizable
npx shadcn@latest add command
```

### Step 2: Install Additional Dependencies

```bash
# Rich text editor for notes
npm install @tiptap/react @tiptap/starter-kit @tiptap/extension-placeholder

# Markdown support
npm install react-markdown remark-gfm

# Icons for UI controls
npm install lucide-react
```

## Phase 2: Enhanced Layout Components

### File 1: Enhanced ThreeColumnLayout Component

**File**: `services/client/src/components/layouts/ThreeColumnLayout.tsx`

**Complete Replacement** (Lines 1-120):

```typescript
import { ReactNode, useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { PanelLeftClose, PanelLeftOpen, PanelRightClose, PanelRightOpen, Maximize, Minimize } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ThreeColumnLayoutProps {
  left: ReactNode;
  center: ReactNode;
  right?: ReactNode;
  gridGap?: number;
  leftColSpan?: number;
  centerColSpan?: number;
  rightColSpan?: number;
  panelPadding?: number;
  // New collapsible props
  leftCollapsible?: boolean;
  rightCollapsible?: boolean;
  leftCollapsed?: boolean;
  rightCollapsed?: boolean;
  onLeftToggle?: () => void;
  onRightToggle?: () => void;
  fullscreenMode?: boolean;
  onFullscreenToggle?: () => void;
  // Mobile responsive props
  mobileBreakpoint?: 'sm' | 'md' | 'lg';
}

export default function ThreeColumnLayout({
  left,
  center,
  right,
  gridGap = 4,
  leftColSpan = 3,
  centerColSpan = 6,
  rightColSpan = 3,
  panelPadding = 4,
  leftCollapsible = false,
  rightCollapsible = false,
  leftCollapsed = false,
  rightCollapsed = false,
  onLeftToggle,
  onRightToggle,
  fullscreenMode = false,
  onFullscreenToggle,
  mobileBreakpoint = 'md',
}: ThreeColumnLayoutProps) {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      const breakpoints = { sm: 640, md: 768, lg: 1024 };
      setIsMobile(window.innerWidth < breakpoints[mobileBreakpoint]);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, [mobileBreakpoint]);

  // Calculate dynamic column spans
  const getColumnSpans = () => {
    if (fullscreenMode) {
      return { left: 0, center: 12, right: 0 };
    }
    
    let availableSpans = 12;
    let leftSpan = leftCollapsed ? 0 : leftColSpan;
    let rightSpan = right && !rightCollapsed ? rightColSpan : 0;
    let centerSpan = availableSpans - leftSpan - rightSpan;
    
    return { left: leftSpan, center: centerSpan, right: rightSpan };
  };

  const { left: leftSpan, center: centerSpan, right: rightSpan } = getColumnSpans();

  // Mobile layout using Sheet components
  if (isMobile) {
    return (
      <div className="h-screen flex flex-col bg-gray-200">
        {/* Mobile Header with Controls */}
        <div className="flex items-center justify-between p-2 bg-white border-b">
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon">
                <PanelLeftOpen className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              {left}
            </SheetContent>
          </Sheet>
          
          {onFullscreenToggle && (
            <Button variant="ghost" size="icon" onClick={onFullscreenToggle}>
              {fullscreenMode ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
          )}
          
          {right && (
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <PanelRightOpen className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-80">
                {right}
              </SheetContent>
            </Sheet>
          )}
        </div>
        
        {/* Mobile Center Content */}
        <div className="flex-1 overflow-hidden">
          {center}
        </div>
      </div>
    );
  }

  // Desktop layout
  return (
    <div
      className="bg-gray-200 mx-auto relative"
      style={{ height: 'calc(100vh - var(--nav-height, 60px))' }}
    >
      <div className={cn("grid h-full p-4 transition-all duration-300", `gap-${gridGap}`, 
        leftSpan > 0 && rightSpan > 0 ? "grid-cols-12" :
        leftSpan > 0 ? "grid-cols-12" : 
        rightSpan > 0 ? "grid-cols-12" : "grid-cols-1"
      )}>
        
        {/* Left Panel */}
        {leftSpan > 0 && (
          <div className={cn(
            "bg-white rounded-md h-full overflow-auto transition-all duration-300",
            `col-span-${leftSpan}`,
            `p-${panelPadding}`,
            "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
          )}>
            {/* Left Panel Toggle Button */}
            {leftCollapsible && (
              <div className="flex justify-end mb-2">
                <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={onLeftToggle}
                  className="h-6 w-6"
                >
                  <PanelLeftClose className="h-3 w-3" />
                </Button>
              </div>
            )}
            {left}
          </div>
        )}

        {/* Collapsed Left Panel Toggle */}
        {leftCollapsible && leftCollapsed && (
          <div className="fixed left-4 top-1/2 transform -translate-y-1/2 z-10">
            <Button 
              variant="outline" 
              size="icon"
              onClick={onLeftToggle}
              className="h-8 w-8 bg-white shadow-md"
            >
              <PanelLeftOpen className="h-4 w-4" />
            </Button>
          </div>
        )}
        
        {/* Center Panel */}
        <div className={cn(
          "overflow-auto h-full transition-all duration-300",
          `col-span-${centerSpan}`,
          "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
        )}>
          {/* Fullscreen Toggle */}
          {onFullscreenToggle && (
            <div className="absolute top-6 right-6 z-10">
              <Button 
                variant="outline" 
                size="icon"
                onClick={onFullscreenToggle}
                className="bg-white/90 backdrop-blur-sm shadow-md"
              >
                {fullscreenMode ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
              </Button>
            </div>
          )}
          {center}
        </div>
        
        {/* Right Panel */}
        {rightSpan > 0 && right && (
          <div className={cn(
            "bg-white rounded-md h-full overflow-auto transition-all duration-300",
            `col-span-${rightSpan}`,
            `p-${panelPadding}`,
            "[&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none']"
          )}>
            {/* Right Panel Toggle Button */}
            {rightCollapsible && (
              <div className="flex justify-start mb-2">
                <Button 
                  variant="ghost" 
                  size="icon"
                  onClick={onRightToggle}
                  className="h-6 w-6"
                >
                  <PanelRightClose className="h-3 w-3" />
                </Button>
              </div>
            )}
            {right}
          </div>
        )}

        {/* Collapsed Right Panel Toggle */}
        {rightCollapsible && rightCollapsed && right && (
          <div className="fixed right-4 top-1/2 transform -translate-y-1/2 z-10">
            <Button 
              variant="outline" 
              size="icon"
              onClick={onRightToggle}
              className="h-8 w-8 bg-white shadow-md"
            >
              <PanelRightOpen className="h-4 w-4" />
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
```

### File 2: Enhanced PanelWithHeader using ShadCN Tabs

**File**: `services/client/src/components/shared/PanelWithHeader.tsx`

**Complete Replacement** (Lines 1-85):

```typescript
import React, { ReactNode } from 'react';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { cn } from '@/lib/utils';

interface Tab {
  id: string;
  label: string;
  content?: ReactNode;
}

interface HeaderButton {
  icon: ReactNode;
  onClick: () => void;
  ariaLabel: string;
  variant?: 'default' | 'ghost' | 'outline';
}

interface PanelWithHeaderProps {
  tabs?: Tab[];
  activeTab?: string;
  onTabChange?: (tabId: string) => void;
  headerButtons?: HeaderButton[];
  children?: ReactNode;
  footer?: ReactNode;
  className?: string;
  collapsible?: boolean;
  collapsed?: boolean;
  onToggle?: () => void;
}

export const PanelWithHeader: React.FC<PanelWithHeaderProps> = ({
  tabs,
  activeTab,
  onTabChange,
  headerButtons,
  children,
  footer,
  className = '',
  collapsible = false,
  collapsed = false,
  onToggle,
}) => {
  if (collapsed) {
    return (
      <div className={cn("h-full flex flex-col", className)}>
        <div className="p-2 border-b">
          {collapsible && onToggle && (
            <Button variant="ghost" size="icon" onClick={onToggle} className="w-full">
              <span className="sr-only">Expand panel</span>
              ⋯
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn("overflow-hidden h-full flex flex-col", className)}>
      {/* Header with Tabs or Buttons */}
      {(tabs || headerButtons) && (
        <div className="border-b border-border">
          <div className="flex justify-between items-center px-4 py-3">
            {/* Tabs using ShadCN */}
            {tabs && (
              <Tabs value={activeTab} onValueChange={onTabChange} className="flex-1">
                <TabsList className="grid w-full grid-cols-2">
                  {tabs.map((tab) => (
                    <TabsTrigger key={tab.id} value={tab.id}>
                      {tab.label}
                    </TabsTrigger>
                  ))}
                </TabsList>
              </Tabs>
            )}

            {/* Header Buttons */}
            <div className="flex items-center space-x-2">
              {headerButtons?.map((button, index) => (
                <Button
                  key={index}
                  variant={button.variant || 'ghost'}
                  size="icon"
                  onClick={button.onClick}
                  aria-label={button.ariaLabel}
                >
                  {button.icon}
                </Button>
              ))}
              
              {collapsible && onToggle && (
                <Button variant="ghost" size="icon" onClick={onToggle}>
                  <span className="sr-only">Collapse panel</span>
                  ×
                </Button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Content Area */}
      {tabs ? (
        <Tabs value={activeTab} onValueChange={onTabChange} className="flex-1 flex flex-col">
          {tabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="flex-1 overflow-y-auto p-4 space-y-4">
              {tab.content || children}
            </TabsContent>
          ))}
        </Tabs>
      ) : (
        <div className="flex-1 overflow-y-auto p-4 space-y-4">{children}</div>
      )}

      {/* Footer */}
      {footer && (
        <>
          <Separator />
          <div className="p-4">{footer}</div>
        </>
      )}
    </div>
  );
};

export default PanelWithHeader;

### File 3: Enhanced OverViewPanel Component

**File**: `services/client/src/components/learning-center/OverViewPanel.tsx`

**Complete Replacement** (Lines 1-60):

```typescript
import React, { useState } from 'react';
import VerticalProgress from '../vertical-progress.component';
import { Course, Lesson } from '../../types/Course';
import { PanelWithHeader } from '../shared/PanelWithHeader';
import { Button } from '@/components/ui/button';
import { PanelLeftClose } from 'lucide-react';

interface OverViewPanelProps {
  course: Course;
  setSelectedLesson: (lesson: Lesson) => void;
  collapsible?: boolean;
  collapsed?: boolean;
  onToggle?: () => void;
}

const OverViewPanel: React.FC<OverViewPanelProps> = ({
  course,
  setSelectedLesson,
  collapsible = false,
  collapsed = false,
  onToggle,
}) => {
  const [activeTab, setActiveTab] = useState('curriculum');

  const tabs = [
    {
      id: 'curriculum',
      label: 'Course Overview',
      content: (
        <VerticalProgress
          modules={course.modules}
          currentModuleId={course.modules[0]._id}
          currentLessonId={course.modules[0].lessons[0]._id}
          onLessonSelect={(lesson) => setSelectedLesson(lesson)}
          completedLessons={course.userProgress?.completedLessons}
        />
      ),
    },
    {
      id: 'progress',
      label: 'Progress',
      content: (
        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            Progress tracking content will go here
          </div>
          {/* Add progress charts, statistics, etc. */}
        </div>
      ),
    },
  ];

  const headerButtons = collapsible ? [
    {
      icon: <PanelLeftClose className="h-4 w-4" />,
      onClick: onToggle || (() => {}),
      ariaLabel: 'Collapse sidebar',
      variant: 'ghost' as const,
    },
  ] : undefined;

  return (
    <PanelWithHeader
      tabs={tabs}
      activeTab={activeTab}
      onTabChange={setActiveTab}
      headerButtons={headerButtons}
      collapsible={collapsible}
      collapsed={collapsed}
      onToggle={onToggle}
    />
  );
};

export default OverViewPanel;
```

### File 4: Enhanced ContentPlayer Component

**File**: `services/client/src/components/ContentPlayer.tsx`

**Modifications** (Lines 1-15, add imports):

```typescript
import React, { useState, useRef, useEffect } from 'react';
import ReactPlayer from 'react-player';
import { Lesson } from '../types/Course';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import {
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  Maximize,
  Minimize,
  StickyNote,
  Play,
  Pause
} from 'lucide-react';
import { cn } from '@/lib/utils';
```

**Modifications** (Lines 16-35, update interface and add state):

```typescript
interface ContentPlayerProps {
  lesson: Lesson;
  onPreviousLesson?: () => void;
  onNextLesson?: () => void;
  onToggleComplete?: (lessonId: string) => void;
  isCompleted?: boolean;
  hasPrevious?: boolean;
  hasNext?: boolean;
  fullscreen?: boolean;
  onFullscreenToggle?: () => void;
  onCreateNote?: (timestamp: number) => void;
}

const ContentPlayer: React.FC<ContentPlayerProps> = ({
  lesson,
  onPreviousLesson,
  onNextLesson,
  onToggleComplete,
  isCompleted = false,
  hasPrevious = false,
  hasNext = false,
  fullscreen = false,
  onFullscreenToggle,
  onCreateNote,
}) => {
  const [ready, setReady] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [playing, setPlaying] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const playerRef = useRef<ReactPlayer>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();
```

**Modifications** (Lines 70-120, update video player section):

```typescript
      <div className={cn(
        "relative group",
        fullscreen ? "fixed inset-0 z-50 bg-black" : "aspect-video bg-gray-100"
      )}
      onMouseMove={() => {
        setShowControls(true);
        if (controlsTimeoutRef.current) {
          clearTimeout(controlsTimeoutRef.current);
        }
        controlsTimeoutRef.current = setTimeout(() => {
          if (playing) setShowControls(false);
        }, 3000);
      }}
      onMouseLeave={() => {
        if (playing) setShowControls(false);
      }}
      >
        <ReactPlayer
          ref={playerRef}
          url={lesson.videoLink}
          controls={false}
          playing={playing}
          width="100%"
          height="100%"
          style={{
            opacity: ready ? 1 : 0,
            transition: 'opacity 0.3s ease-in-out',
          }}
          onReady={() => setReady(true)}
          onProgress={(state) => setCurrentTime(state.playedSeconds)}
          onPlay={() => setPlaying(true)}
          onPause={() => setPlaying(false)}
          config={{
            youtube: {
              playerVars: { disablekb: 1 },
            },
          }}
        />

        {/* Custom Video Controls Overlay */}
        <div className={cn(
          "absolute inset-0 flex items-center justify-center transition-opacity duration-300",
          showControls ? "opacity-100" : "opacity-0"
        )}>
          {/* Play/Pause Button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setPlaying(!playing)}
            className="h-16 w-16 bg-black/50 hover:bg-black/70 text-white rounded-full"
          >
            {playing ? <Pause className="h-8 w-8" /> : <Play className="h-8 w-8" />}
          </Button>
        </div>

        {/* Top Controls */}
        <div className={cn(
          "absolute top-4 right-4 flex space-x-2 transition-opacity duration-300",
          showControls ? "opacity-100" : "opacity-0"
        )}>
          {/* Create Note Button */}
          {onCreateNote && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => onCreateNote(currentTime)}
              className="bg-white/90 backdrop-blur-sm hover:bg-white"
            >
              <StickyNote className="h-4 w-4" />
            </Button>
          )}

          {/* Fullscreen Toggle */}
          {onFullscreenToggle && (
            <Button
              variant="outline"
              size="icon"
              onClick={onFullscreenToggle}
              className="bg-white/90 backdrop-blur-sm hover:bg-white"
            >
              {fullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </Button>
          )}
        </div>

        {/* Bottom Progress Bar */}
        <div className={cn(
          "absolute bottom-0 left-0 right-0 h-1 bg-black/30 transition-opacity duration-300",
          showControls ? "opacity-100" : "opacity-0"
        )}>
          <div
            className="h-full bg-primary transition-all duration-300"
            style={{ width: `${(currentTime / (playerRef.current?.getDuration() || 1)) * 100}%` }}
          />
        </div>
      </div>

## Phase 3: Note-Taking Components

### File 5: Note Types and Interfaces

**File**: `services/client/src/types/Note.ts`

**New File** (Lines 1-45):

```typescript
export interface LessonNote {
  _id: string;
  title: string;
  content: string;
  lessonId: string;
  courseId: string;
  timestamp?: number;
  isPublic: boolean;
  tags: string[];
  type: 'personal' | 'shared' | 'highlight';
  createdBy: string;
  created: Date;
  updated: Date;
}

export interface CreateNoteData {
  title: string;
  content: string;
  lessonId: string;
  courseId: string;
  timestamp?: number;
  isPublic?: boolean;
  tags?: string[];
  type?: 'personal' | 'shared' | 'highlight';
}

export interface UpdateNoteData {
  title?: string;
  content?: string;
  timestamp?: number;
  isPublic?: boolean;
  tags?: string[];
  type?: 'personal' | 'shared' | 'highlight';
}

export interface NoteFilter {
  type?: 'personal' | 'shared' | 'highlight' | 'all';
  tags?: string[];
  searchQuery?: string;
  timeRange?: {
    start: number;
    end: number;
  };
}
```

### File 6: Note Service

**File**: `services/client/src/api/services/NoteService.ts`

**New File** (Lines 1-80):

```typescript
import { LessonNote, CreateNoteData, UpdateNoteData, NoteFilter } from '../../types/Note';

const API_BASE = '/api';

export class NoteService {
  static async getLessonNotes(lessonId: string, filter?: NoteFilter): Promise<LessonNote[]> {
    const params = new URLSearchParams();
    if (filter?.type && filter.type !== 'all') params.append('type', filter.type);
    if (filter?.tags?.length) params.append('tags', filter.tags.join(','));
    if (filter?.searchQuery) params.append('search', filter.searchQuery);
    if (filter?.timeRange) {
      params.append('timeStart', filter.timeRange.start.toString());
      params.append('timeEnd', filter.timeRange.end.toString());
    }

    const response = await fetch(`${API_BASE}/notes/lesson/${lessonId}?${params}`);
    if (!response.ok) throw new Error('Failed to fetch lesson notes');
    return response.json();
  }

  static async getPublicLessonNotes(lessonId: string): Promise<LessonNote[]> {
    const response = await fetch(`${API_BASE}/notes/lesson/${lessonId}/public`);
    if (!response.ok) throw new Error('Failed to fetch public notes');
    return response.json();
  }

  static async createLessonNote(noteData: CreateNoteData): Promise<LessonNote> {
    const response = await fetch(`${API_BASE}/notes/lesson/${noteData.lessonId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(noteData),
    });
    if (!response.ok) throw new Error('Failed to create note');
    return response.json();
  }

  static async updateNote(noteId: string, noteData: UpdateNoteData): Promise<LessonNote> {
    const response = await fetch(`${API_BASE}/notes/${noteId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(noteData),
    });
    if (!response.ok) throw new Error('Failed to update note');
    return response.json();
  }

  static async deleteNote(noteId: string): Promise<void> {
    const response = await fetch(`${API_BASE}/notes/${noteId}`, {
      method: 'DELETE',
    });
    if (!response.ok) throw new Error('Failed to delete note');
  }

  static async toggleNoteVisibility(noteId: string): Promise<LessonNote> {
    const response = await fetch(`${API_BASE}/notes/${noteId}/visibility`, {
      method: 'PUT',
    });
    if (!response.ok) throw new Error('Failed to toggle note visibility');
    return response.json();
  }

  static async getNotesByTimestamp(
    lessonId: string,
    timestamp: number,
    range: number = 30
  ): Promise<LessonNote[]> {
    const response = await fetch(
      `${API_BASE}/notes/lesson/${lessonId}/timestamp/${timestamp}?range=${range}`
    );
    if (!response.ok) throw new Error('Failed to fetch notes by timestamp');
    return response.json();
  }

  static formatTimestamp(seconds: number): string {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }
}
```

### File 7: Note Editor Component

**File**: `services/client/src/components/learning-center/NoteEditor.tsx`

**New File** (Lines 1-150):

```typescript
import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import {
  Save,
  X,
  Tag,
  Clock,
  Eye,
  EyeOff,
  Type,
  Hash
} from 'lucide-react';
import { LessonNote, CreateNoteData, UpdateNoteData } from '../../types/Note';
import { NoteService } from '../../api/services/NoteService';
import { cn } from '@/lib/utils';

interface NoteEditorProps {
  isOpen: boolean;
  onClose: () => void;
  lessonId: string;
  courseId: string;
  timestamp?: number;
  existingNote?: LessonNote;
  onSave: (note: LessonNote) => void;
}

export const NoteEditor: React.FC<NoteEditorProps> = ({
  isOpen,
  onClose,
  lessonId,
  courseId,
  timestamp,
  existingNote,
  onSave,
}) => {
  const [title, setTitle] = useState('');
  const [content, setContent] = useState('');
  const [isPublic, setIsPublic] = useState(false);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [noteType, setNoteType] = useState<'personal' | 'shared' | 'highlight'>('personal');
  const [isSaving, setIsSaving] = useState(false);

  useEffect(() => {
    if (existingNote) {
      setTitle(existingNote.title);
      setContent(existingNote.content);
      setIsPublic(existingNote.isPublic);
      setTags(existingNote.tags);
      setNoteType(existingNote.type);
    } else {
      // Reset form for new note
      setTitle('');
      setContent('');
      setIsPublic(false);
      setTags([]);
      setNoteType('personal');
    }
  }, [existingNote, isOpen]);

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const handleSave = async () => {
    if (!title.trim() || !content.trim()) return;

    setIsSaving(true);
    try {
      let savedNote: LessonNote;

      if (existingNote) {
        // Update existing note
        const updateData: UpdateNoteData = {
          title: title.trim(),
          content: content.trim(),
          isPublic,
          tags,
          type: noteType,
        };
        savedNote = await NoteService.updateNote(existingNote._id, updateData);
      } else {
        // Create new note
        const createData: CreateNoteData = {
          title: title.trim(),
          content: content.trim(),
          lessonId,
          courseId,
          timestamp,
          isPublic,
          tags,
          type: noteType,
        };
        savedNote = await NoteService.createLessonNote(createData);
      }

      onSave(savedNote);
      onClose();
    } catch (error) {
      console.error('Failed to save note:', error);
      // TODO: Add toast notification for error
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            {existingNote ? 'Edit Note' : 'Create Note'}
            {timestamp && (
              <Badge variant="outline" className="ml-auto">
                <Clock className="h-3 w-3 mr-1" />
                {NoteService.formatTimestamp(timestamp)}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Note Title */}
          <div className="space-y-2">
            <Label htmlFor="note-title">Title</Label>
            <Input
              id="note-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="Enter note title..."
              className="w-full"
            />
          </div>

          {/* Note Type Tabs */}
          <div className="space-y-2">
            <Label>Note Type</Label>
            <Tabs value={noteType} onValueChange={(value) => setNoteType(value as any)}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="personal">Personal</TabsTrigger>
                <TabsTrigger value="shared">Shared</TabsTrigger>
                <TabsTrigger value="highlight">Highlight</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Note Content */}
          <div className="space-y-2">
            <Label htmlFor="note-content">Content</Label>
            <Textarea
              id="note-content"
              value={content}
              onChange={(e) => setContent(e.target.value)}
              placeholder="Write your note here..."
              className="min-h-[200px] resize-none"
            />
          </div>

          {/* Tags */}
          <div className="space-y-2">
            <Label>Tags</Label>
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add tag..."
                onKeyPress={(e) => e.key === 'Enter' && handleAddTag()}
                className="flex-1"
              />
              <Button onClick={handleAddTag} variant="outline" size="icon">
                <Hash className="h-4 w-4" />
              </Button>
            </div>
            {tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    <Tag className="h-3 w-3" />
                    {tag}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveTag(tag)}
                      className="h-4 w-4 p-0 ml-1"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
          </div>

          <Separator />

          {/* Visibility Toggle */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {isPublic ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
              <Label htmlFor="note-visibility">
                {isPublic ? 'Public Note' : 'Private Note'}
              </Label>
            </div>
            <Switch
              id="note-visibility"
              checked={isPublic}
              onCheckedChange={setIsPublic}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!title.trim() || !content.trim() || isSaving}
          >
            {isSaving ? 'Saving...' : 'Save Note'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
```

### File 8: Notes Panel Component

**File**: `services/client/src/components/learning-center/NotesPanel.tsx`

**New File** (Lines 1-200):

```typescript
import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Plus,
  Search,
  Filter,
  Clock,
  Eye,
  EyeOff,
  Edit,
  Trash2,
  Tag,
  MessageSquare
} from 'lucide-react';
import { LessonNote, NoteFilter } from '../../types/Note';
import { NoteService } from '../../api/services/NoteService';
import { NoteEditor } from './NoteEditor';
import { cn } from '@/lib/utils';

interface NotesPanelProps {
  lessonId: string;
  courseId: string;
  currentTimestamp?: number;
  onNoteClick?: (timestamp: number) => void;
  className?: string;
}

export const NotesPanel: React.FC<NotesPanelProps> = ({
  lessonId,
  courseId,
  currentTimestamp = 0,
  onNoteClick,
  className,
}) => {
  const [notes, setNotes] = useState<LessonNote[]>([]);
  const [publicNotes, setPublicNotes] = useState<LessonNote[]>([]);
  const [filteredNotes, setFilteredNotes] = useState<LessonNote[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('personal');
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [editingNote, setEditingNote] = useState<LessonNote | undefined>();
  const [filter, setFilter] = useState<NoteFilter>({ type: 'all' });

  useEffect(() => {
    loadNotes();
  }, [lessonId]);

  useEffect(() => {
    applyFilters();
  }, [notes, publicNotes, searchQuery, activeTab, filter]);

  const loadNotes = async () => {
    setIsLoading(true);
    try {
      const [personalNotes, sharedNotes] = await Promise.all([
        NoteService.getLessonNotes(lessonId),
        NoteService.getPublicLessonNotes(lessonId),
      ]);
      setNotes(personalNotes);
      setPublicNotes(sharedNotes);
    } catch (error) {
      console.error('Failed to load notes:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const applyFilters = () => {
    const allNotes = activeTab === 'personal' ? notes : publicNotes;

    let filtered = allNotes.filter((note) => {
      const matchesSearch = !searchQuery ||
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase());

      const matchesType = filter.type === 'all' || note.type === filter.type;

      return matchesSearch && matchesType;
    });

    // Sort by timestamp if available, otherwise by creation date
    filtered.sort((a, b) => {
      if (a.timestamp && b.timestamp) {
        return a.timestamp - b.timestamp;
      }
      return new Date(b.created).getTime() - new Date(a.created).getTime();
    });

    setFilteredNotes(filtered);
  };

  const handleCreateNote = () => {
    setEditingNote(undefined);
    setIsEditorOpen(true);
  };

  const handleEditNote = (note: LessonNote) => {
    setEditingNote(note);
    setIsEditorOpen(true);
  };

  const handleDeleteNote = async (noteId: string) => {
    try {
      await NoteService.deleteNote(noteId);
      await loadNotes();
    } catch (error) {
      console.error('Failed to delete note:', error);
    }
  };

  const handleNoteSaved = async (savedNote: LessonNote) => {
    await loadNotes();
  };

  const handleNoteTimestampClick = (timestamp: number) => {
    if (onNoteClick) {
      onNoteClick(timestamp);
    }
  };

  const renderNoteCard = (note: LessonNote) => (
    <Card key={note._id} className="mb-3">
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium text-sm line-clamp-1">{note.title}</h4>
            <div className="flex items-center gap-2 mt-1">
              {note.timestamp && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleNoteTimestampClick(note.timestamp!)}
                  className="h-6 px-2 text-xs"
                >
                  <Clock className="h-3 w-3 mr-1" />
                  {NoteService.formatTimestamp(note.timestamp)}
                </Button>
              )}
              <Badge variant="outline" className="text-xs">
                {note.type}
              </Badge>
              {note.isPublic ? (
                <Eye className="h-3 w-3 text-muted-foreground" />
              ) : (
                <EyeOff className="h-3 w-3 text-muted-foreground" />
              )}
            </div>
          </div>
          <div className="flex gap-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleEditNote(note)}
              className="h-6 w-6 p-0"
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleDeleteNote(note._id)}
              className="h-6 w-6 p-0 text-destructive"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <p className="text-sm text-muted-foreground line-clamp-3 mb-2">
          {note.content}
        </p>
        {note.tags.length > 0 && (
          <div className="flex flex-wrap gap-1">
            {note.tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="text-xs">
                <Tag className="h-2 w-2 mr-1" />
                {tag}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className={cn("h-full flex flex-col", className)}>
      {/* Header */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Notes
          </h3>
          <Button size="sm" onClick={handleCreateNote}>
            <Plus className="h-4 w-4 mr-1" />
            Add Note
          </Button>
        </div>

        {/* Search */}
        <div className="relative mb-3">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search notes..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="personal">My Notes</TabsTrigger>
            <TabsTrigger value="public">Public Notes</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Notes List */}
      <ScrollArea className="flex-1 p-4">
        {isLoading ? (
          <div className="text-center text-muted-foreground">Loading notes...</div>
        ) : filteredNotes.length === 0 ? (
          <div className="text-center text-muted-foreground">
            {searchQuery ? 'No notes match your search.' : 'No notes yet.'}
          </div>
        ) : (
          <div className="space-y-3">
            {filteredNotes.map(renderNoteCard)}
          </div>
        )}
      </ScrollArea>

      {/* Note Editor */}
      <NoteEditor
        isOpen={isEditorOpen}
        onClose={() => setIsEditorOpen(false)}
        lessonId={lessonId}
        courseId={courseId}
        timestamp={currentTimestamp}
        existingNote={editingNote}
        onSave={handleNoteSaved}
      />
    </div>
  );
};
```

## Phase 4: Updated Main Learning Center Page

### File 9: Enhanced LearningCenter Page

**File**: `services/client/src/pages/LearningCenter.tsx`

**Complete Replacement** (Lines 1-120):

```typescript
import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import ThreeColumnLayout from '../components/layouts/ThreeColumnLayout';
import OverViewPanel from '../components/learning-center/OverViewPanel';
import ContentPlayer from '../components/ContentPlayer';
import { NotesPanel } from '../components/learning-center/NotesPanel';
import { Course, Lesson } from '../types/Course';
import { Button } from '@/components/ui/button';
import { PanelLeftOpen, PanelRightOpen, StickyNote } from 'lucide-react';
import { NoteEditor } from '../components/learning-center/NoteEditor';

const LearningCenter: React.FC = () => {
  const { courseId } = useParams<{ courseId: string }>();
  const [course, setCourse] = useState<Course | null>(null);
  const [selectedLesson, setSelectedLesson] = useState<Lesson | null>(null);
  const [loading, setLoading] = useState(true);

  // Layout state
  const [leftCollapsed, setLeftCollapsed] = useState(false);
  const [rightCollapsed, setRightCollapsed] = useState(false);
  const [fullscreenMode, setFullscreenMode] = useState(false);

  // Note-taking state
  const [isNoteEditorOpen, setIsNoteEditorOpen] = useState(false);
  const [noteTimestamp, setNoteTimestamp] = useState<number>(0);
  const [currentVideoTime, setCurrentVideoTime] = useState<number>(0);

  useEffect(() => {
    if (courseId) {
      fetchCourse(courseId);
    }
  }, [courseId]);

  const fetchCourse = async (id: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/courses/${id}`);
      if (response.ok) {
        const courseData = await response.json();
        setCourse(courseData);

        // Set the first lesson as selected by default
        if (courseData.modules && courseData.modules.length > 0) {
          const firstModule = courseData.modules[0];
          if (firstModule.lessons && firstModule.lessons.length > 0) {
            setSelectedLesson(firstModule.lessons[0]);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching course:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNoteAtTimestamp = (timestamp: number) => {
    setNoteTimestamp(timestamp);
    setIsNoteEditorOpen(true);
  };

  const handleNoteTimestampClick = (timestamp: number) => {
    // This would seek the video to the specific timestamp
    // Implementation depends on your video player setup
    setCurrentVideoTime(timestamp);
  };

  const handleToggleComplete = (lessonId: string) => {
    // Implementation for marking lesson as complete
    console.log('Toggle complete for lesson:', lessonId);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg">Loading course...</div>
      </div>
    );
  }

  if (!course) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-lg">Course not found</div>
      </div>
    );
  }

  return (
    <div className="h-screen">
      <ThreeColumnLayout
        left={
          <OverViewPanel
            course={course}
            setSelectedLesson={setSelectedLesson}
            collapsible={true}
            collapsed={leftCollapsed}
            onToggle={() => setLeftCollapsed(!leftCollapsed)}
          />
        }
        center={
          selectedLesson ? (
            <ContentPlayer
              lesson={selectedLesson}
              onToggleComplete={handleToggleComplete}
              isCompleted={course.userProgress?.completedLessons?.includes(selectedLesson._id)}
              fullscreen={fullscreenMode}
              onFullscreenToggle={() => setFullscreenMode(!fullscreenMode)}
              onCreateNote={handleCreateNoteAtTimestamp}
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <h2 className="text-xl font-semibold mb-2">Select a lesson to begin</h2>
                <p className="text-muted-foreground">
                  Choose a lesson from the course overview to start learning.
                </p>
              </div>
            </div>
          )
        }
        right={
          selectedLesson && (
            <NotesPanel
              lessonId={selectedLesson._id}
              courseId={course._id}
              currentTimestamp={currentVideoTime}
              onNoteClick={handleNoteTimestampClick}
            />
          )
        }
        leftCollapsible={true}
        rightCollapsible={true}
        leftCollapsed={leftCollapsed}
        rightCollapsed={rightCollapsed}
        onLeftToggle={() => setLeftCollapsed(!leftCollapsed)}
        onRightToggle={() => setRightCollapsed(!rightCollapsed)}
        fullscreenMode={fullscreenMode}
        onFullscreenToggle={() => setFullscreenMode(!fullscreenMode)}
        leftColSpan={3}
        centerColSpan={6}
        rightColSpan={3}
      />

      {/* Note Editor for timestamp-based notes */}
      {selectedLesson && (
        <NoteEditor
          isOpen={isNoteEditorOpen}
          onClose={() => setIsNoteEditorOpen(false)}
          lessonId={selectedLesson._id}
          courseId={course._id}
          timestamp={noteTimestamp}
          onSave={() => {
            // Refresh notes panel or handle note creation
            setIsNoteEditorOpen(false);
          }}
        />
      )}
    </div>
  );
};

export default LearningCenter;
```

## Phase 5: Backend API Enhancements

### File 10: Enhanced Note Schema

**File**: `services/api/src/models/NoteSchema.js`

**Complete Replacement** (Lines 1-50):

```javascript
const mongoose = require('mongoose');

const noteSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  content: {
    type: String,
    required: true,
    maxlength: 5000
  },
  lessonId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Lesson',
    required: true,
    index: true
  },
  courseId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Course',
    required: true,
    index: true
  },
  timestamp: {
    type: Number,
    min: 0,
    index: true
  },
  isPublic: {
    type: Boolean,
    default: false,
    index: true
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 50
  }],
  type: {
    type: String,
    enum: ['personal', 'shared', 'highlight'],
    default: 'personal',
    index: true
  },
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  }
}, {
  timestamps: { createdAt: 'created', updatedAt: 'updated' }
});

// Compound indexes for efficient queries
noteSchema.index({ lessonId: 1, createdBy: 1 });
noteSchema.index({ lessonId: 1, isPublic: 1 });
noteSchema.index({ lessonId: 1, timestamp: 1 });
noteSchema.index({ courseId: 1, createdBy: 1 });

module.exports = mongoose.model('Note', noteSchema);
```

### File 11: Enhanced Note Service

**File**: `services/api/src/services/note/NoteService.js`

**Complete Replacement** (Lines 1-150):

```javascript
const Note = require('../../models/NoteSchema');
const { ObjectId } = require('mongoose').Types;

class NoteService {
  // Get all notes for a lesson (user's own notes)
  static async getLessonNotes(lessonId, userId, filter = {}) {
    const query = {
      lessonId: new ObjectId(lessonId),
      createdBy: new ObjectId(userId)
    };

    // Apply filters
    if (filter.type && filter.type !== 'all') {
      query.type = filter.type;
    }

    if (filter.tags && filter.tags.length > 0) {
      query.tags = { $in: filter.tags };
    }

    if (filter.timeRange) {
      query.timestamp = {
        $gte: filter.timeRange.start,
        $lte: filter.timeRange.end
      };
    }

    let notesQuery = Note.find(query).populate('createdBy', 'name email');

    // Apply search if provided
    if (filter.searchQuery) {
      notesQuery = notesQuery.find({
        $or: [
          { title: { $regex: filter.searchQuery, $options: 'i' } },
          { content: { $regex: filter.searchQuery, $options: 'i' } }
        ]
      });
    }

    return await notesQuery.sort({ timestamp: 1, created: -1 }).exec();
  }

  // Get public notes for a lesson
  static async getPublicLessonNotes(lessonId, filter = {}) {
    const query = {
      lessonId: new ObjectId(lessonId),
      isPublic: true
    };

    // Apply filters
    if (filter.type && filter.type !== 'all') {
      query.type = filter.type;
    }

    if (filter.tags && filter.tags.length > 0) {
      query.tags = { $in: filter.tags };
    }

    let notesQuery = Note.find(query).populate('createdBy', 'name email');

    // Apply search if provided
    if (filter.searchQuery) {
      notesQuery = notesQuery.find({
        $or: [
          { title: { $regex: filter.searchQuery, $options: 'i' } },
          { content: { $regex: filter.searchQuery, $options: 'i' } }
        ]
      });
    }

    return await notesQuery.sort({ timestamp: 1, created: -1 }).exec();
  }

  // Create a new note
  static async createLessonNote(noteData, userId) {
    const note = new Note({
      ...noteData,
      createdBy: new ObjectId(userId)
    });

    await note.save();
    return await Note.findById(note._id).populate('createdBy', 'name email');
  }

  // Update a note
  static async updateNote(noteId, updateData, userId) {
    const note = await Note.findOneAndUpdate(
      {
        _id: new ObjectId(noteId),
        createdBy: new ObjectId(userId)
      },
      updateData,
      { new: true }
    ).populate('createdBy', 'name email');

    if (!note) {
      throw new Error('Note not found or unauthorized');
    }

    return note;
  }

  // Delete a note
  static async deleteNote(noteId, userId) {
    const result = await Note.findOneAndDelete({
      _id: new ObjectId(noteId),
      createdBy: new ObjectId(userId)
    });

    if (!result) {
      throw new Error('Note not found or unauthorized');
    }

    return result;
  }

  // Toggle note visibility
  static async toggleNoteVisibility(noteId, userId) {
    const note = await Note.findOne({
      _id: new ObjectId(noteId),
      createdBy: new ObjectId(userId)
    });

    if (!note) {
      throw new Error('Note not found or unauthorized');
    }

    note.isPublic = !note.isPublic;
    await note.save();

    return await Note.findById(note._id).populate('createdBy', 'name email');
  }

  // Get notes by timestamp range
  static async getNotesByTimestamp(lessonId, userId, timestamp, range = 30) {
    const startTime = Math.max(0, timestamp - range);
    const endTime = timestamp + range;

    return await Note.find({
      lessonId: new ObjectId(lessonId),
      createdBy: new ObjectId(userId),
      timestamp: {
        $gte: startTime,
        $lte: endTime
      }
    })
    .populate('createdBy', 'name email')
    .sort({ timestamp: 1 })
    .exec();
  }

  // Get all notes for a course
  static async getCourseNotes(courseId, userId, filter = {}) {
    const query = {
      courseId: new ObjectId(courseId),
      createdBy: new ObjectId(userId)
    };

    // Apply filters
    if (filter.type && filter.type !== 'all') {
      query.type = filter.type;
    }

    if (filter.tags && filter.tags.length > 0) {
      query.tags = { $in: filter.tags };
    }

    let notesQuery = Note.find(query)
      .populate('createdBy', 'name email')
      .populate('lessonId', 'title');

    // Apply search if provided
    if (filter.searchQuery) {
      notesQuery = notesQuery.find({
        $or: [
          { title: { $regex: filter.searchQuery, $options: 'i' } },
          { content: { $regex: filter.searchQuery, $options: 'i' } }
        ]
      });
    }

    return await notesQuery.sort({ created: -1 }).exec();
  }

  // Get note statistics for a user
  static async getNoteStats(userId) {
    const stats = await Note.aggregate([
      { $match: { createdBy: new ObjectId(userId) } },
      {
        $group: {
          _id: null,
          totalNotes: { $sum: 1 },
          publicNotes: {
            $sum: { $cond: [{ $eq: ['$isPublic', true] }, 1, 0] }
          },
          privateNotes: {
            $sum: { $cond: [{ $eq: ['$isPublic', false] }, 1, 0] }
          },
          notesByType: {
            $push: '$type'
          }
        }
      }
    ]);

    return stats[0] || {
      totalNotes: 0,
      publicNotes: 0,
      privateNotes: 0,
      notesByType: []
    };
  }
}

module.exports = NoteService;
```

### File 12: Enhanced Note Routes

**File**: `services/api/src/routes/note/noteRoutes.js`

**Complete Replacement** (Lines 1-120):

```javascript
const express = require('express');
const router = express.Router();
const NoteService = require('../../services/note/NoteService');
const { authenticate } = require('../../middleware/auth');
const { validateNote, validateNoteUpdate } = require('../../middleware/validation');

// Get lesson notes (user's own notes)
router.get('/lesson/:lessonId', authenticate, async (req, res) => {
  try {
    const { lessonId } = req.params;
    const userId = req.user.id;

    // Parse query filters
    const filter = {};
    if (req.query.type) filter.type = req.query.type;
    if (req.query.tags) filter.tags = req.query.tags.split(',');
    if (req.query.search) filter.searchQuery = req.query.search;
    if (req.query.timeStart && req.query.timeEnd) {
      filter.timeRange = {
        start: parseFloat(req.query.timeStart),
        end: parseFloat(req.query.timeEnd)
      };
    }

    const notes = await NoteService.getLessonNotes(lessonId, userId, filter);
    res.json(notes);
  } catch (error) {
    console.error('Error fetching lesson notes:', error);
    res.status(500).json({ error: 'Failed to fetch notes' });
  }
});

// Get public lesson notes
router.get('/lesson/:lessonId/public', authenticate, async (req, res) => {
  try {
    const { lessonId } = req.params;

    // Parse query filters
    const filter = {};
    if (req.query.type) filter.type = req.query.type;
    if (req.query.tags) filter.tags = req.query.tags.split(',');
    if (req.query.search) filter.searchQuery = req.query.search;

    const notes = await NoteService.getPublicLessonNotes(lessonId, filter);
    res.json(notes);
  } catch (error) {
    console.error('Error fetching public notes:', error);
    res.status(500).json({ error: 'Failed to fetch public notes' });
  }
});

// Create a new note for a lesson
router.post('/lesson/:lessonId', authenticate, validateNote, async (req, res) => {
  try {
    const { lessonId } = req.params;
    const userId = req.user.id;

    const noteData = {
      ...req.body,
      lessonId
    };

    const note = await NoteService.createLessonNote(noteData, userId);
    res.status(201).json(note);
  } catch (error) {
    console.error('Error creating note:', error);
    res.status(500).json({ error: 'Failed to create note' });
  }
});

// Update a note
router.put('/:noteId', authenticate, validateNoteUpdate, async (req, res) => {
  try {
    const { noteId } = req.params;
    const userId = req.user.id;

    const note = await NoteService.updateNote(noteId, req.body, userId);
    res.json(note);
  } catch (error) {
    console.error('Error updating note:', error);
    if (error.message === 'Note not found or unauthorized') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Failed to update note' });
    }
  }
});

// Delete a note
router.delete('/:noteId', authenticate, async (req, res) => {
  try {
    const { noteId } = req.params;
    const userId = req.user.id;

    await NoteService.deleteNote(noteId, userId);
    res.status(204).send();
  } catch (error) {
    console.error('Error deleting note:', error);
    if (error.message === 'Note not found or unauthorized') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Failed to delete note' });
    }
  }
});

// Toggle note visibility
router.put('/:noteId/visibility', authenticate, async (req, res) => {
  try {
    const { noteId } = req.params;
    const userId = req.user.id;

    const note = await NoteService.toggleNoteVisibility(noteId, userId);
    res.json(note);
  } catch (error) {
    console.error('Error toggling note visibility:', error);
    if (error.message === 'Note not found or unauthorized') {
      res.status(404).json({ error: error.message });
    } else {
      res.status(500).json({ error: 'Failed to toggle note visibility' });
    }
  }
});

// Get notes by timestamp
router.get('/lesson/:lessonId/timestamp/:timestamp', authenticate, async (req, res) => {
  try {
    const { lessonId, timestamp } = req.params;
    const userId = req.user.id;
    const range = parseInt(req.query.range) || 30;

    const notes = await NoteService.getNotesByTimestamp(
      lessonId,
      userId,
      parseFloat(timestamp),
      range
    );
    res.json(notes);
  } catch (error) {
    console.error('Error fetching notes by timestamp:', error);
    res.status(500).json({ error: 'Failed to fetch notes by timestamp' });
  }
});

// Get all notes for a course
router.get('/course/:courseId', authenticate, async (req, res) => {
  try {
    const { courseId } = req.params;
    const userId = req.user.id;

    // Parse query filters
    const filter = {};
    if (req.query.type) filter.type = req.query.type;
    if (req.query.tags) filter.tags = req.query.tags.split(',');
    if (req.query.search) filter.searchQuery = req.query.search;

    const notes = await NoteService.getCourseNotes(courseId, userId, filter);
    res.json(notes);
  } catch (error) {
    console.error('Error fetching course notes:', error);
    res.status(500).json({ error: 'Failed to fetch course notes' });
  }
});

// Get note statistics
router.get('/stats', authenticate, async (req, res) => {
  try {
    const userId = req.user.id;
    const stats = await NoteService.getNoteStats(userId);
    res.json(stats);
  } catch (error) {
    console.error('Error fetching note stats:', error);
    res.status(500).json({ error: 'Failed to fetch note statistics' });
  }
});

module.exports = router;
```

## Phase 6: Implementation Steps Summary

### Step-by-Step Implementation Checklist

1. **Setup ShadCN Components**
   - [ ] Run `npx shadcn@latest add textarea accordion resizable command`
   - [ ] Install additional dependencies: `@tiptap/react`, `react-markdown`

2. **Update Layout Components**
   - [ ] Replace `ThreeColumnLayout.tsx` with enhanced version
   - [ ] Replace `PanelWithHeader.tsx` with ShadCN Tabs version
   - [ ] Update `OverViewPanel.tsx` with collapsible functionality

3. **Enhance Video Player**
   - [ ] Update `ContentPlayer.tsx` with fullscreen and note creation
   - [ ] Add custom video controls overlay
   - [ ] Implement timestamp-based note creation

4. **Create Note-Taking Components**
   - [ ] Create `Note.ts` types file
   - [ ] Create `NoteService.ts` API service
   - [ ] Create `NoteEditor.tsx` component
   - [ ] Create `NotesPanel.tsx` component

5. **Update Main Page**
   - [ ] Replace `LearningCenter.tsx` with enhanced version
   - [ ] Add state management for collapsible panels
   - [ ] Integrate note-taking functionality

6. **Backend Enhancements**
   - [ ] Update `NoteSchema.js` with new fields
   - [ ] Replace `NoteService.js` with enhanced version
   - [ ] Update `noteRoutes.js` with new endpoints

7. **Testing and Validation**
   - [ ] Test collapsible panel functionality
   - [ ] Test note creation and editing
   - [ ] Test timestamp-based note navigation
   - [ ] Test mobile responsive behavior
   - [ ] Validate API endpoints

### Key Features Implemented

#### Collapsible Layout
- ✅ Dynamic column span calculation
- ✅ Smooth animations with CSS transitions
- ✅ Mobile-responsive with ShadCN Sheet components
- ✅ Fullscreen mode for video player
- ✅ Persistent toggle buttons for collapsed panels

#### Note-Taking System
- ✅ Timestamp-based notes linked to video position
- ✅ Rich text editing with ShadCN components
- ✅ Public/private note visibility
- ✅ Tag-based organization
- ✅ Search and filter functionality
- ✅ Note types (personal, shared, highlight)

#### ShadCN Integration
- ✅ Consistent design system
- ✅ Accessible components with ARIA attributes
- ✅ Built-in animations and transitions
- ✅ Mobile-responsive patterns
- ✅ Customizable theming

### Next Steps After Implementation

1. **Add Rich Text Editor**
   - Integrate @tiptap/react for advanced note editing
   - Add formatting toolbar and markdown support

2. **Implement Real-time Features**
   - WebSocket integration for collaborative notes
   - Real-time note sharing and comments

3. **Enhanced Search**
   - Full-text search across all notes
   - Advanced filtering and sorting options

4. **Analytics and Progress Tracking**
   - Note-taking analytics
   - Learning progress visualization

5. **Export and Sharing**
   - Export notes to PDF/Markdown
   - Share note collections with other users
```
```
